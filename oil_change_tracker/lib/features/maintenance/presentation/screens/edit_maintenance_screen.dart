import 'dart:io';
import 'dart:developer' as dev;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../../../core/providers/auth_provider.dart';
import '../../../../core/models/maintenance_model.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../../core/utils/permission_handler.dart';
import '../../../../generated/app_localizations.dart';
import '../../../../shared/widgets/custom_text_field.dart';
import '../../../car_management/providers/car_provider.dart';
import '../../providers/maintenance_provider.dart';
import '../../data/services/maintenance_photo_service.dart';
import '../../data/repositories/maintenance_repository.dart';
import '../widgets/maintenance_photo_picker.dart';

class EditMaintenanceScreen extends ConsumerStatefulWidget {
  final String maintenanceId;
  final String carId;

  const EditMaintenanceScreen({
    super.key,
    required this.maintenanceId,
    required this.carId,
  });

  @override
  ConsumerState<EditMaintenanceScreen> createState() =>
      _EditMaintenanceScreenState();
}

class _EditMaintenanceScreenState extends ConsumerState<EditMaintenanceScreen> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _mileageController = TextEditingController();
  final _costController = TextEditingController();
  final _serviceProviderController = TextEditingController();
  final _notesController = TextEditingController();
  DateTime _date = DateTime.now();
  String _selectedMaintenanceType = 'generalService';
  bool _isLoading = false;
  bool _dataLoaded = false;
  MaintenanceModel? _originalMaintenance;

  // List to store selected photo files
  final List<File> _selectedPhotoFiles = [];

  // List to store existing photo URLs
  List<String> _existingPhotoUrls = [];

  // List to store photo URLs that should be deleted
  final List<String> _photosToDelete = [];

  List<String> _getMaintenanceTypes() {
    return [
      'generalService',
      'brakeService',
      'engineService',
      'transmissionService',
      'tireService',
      'batteryService',
      'airConditioning',
      'electricalSystem',
      'suspension',
      'exhaustSystem',
      'fuelSystem',
      'coolingSystem',
      'regularMaintenance',
      'other'
    ];
  }

  String _getLocalizedMaintenanceType(String type, S l10n) {
    switch (type) {
      case 'generalService':
        return l10n.generalService;
      case 'brakeService':
        return l10n.brakeService;
      case 'engineService':
        return l10n.engineService;
      case 'transmissionService':
        return l10n.transmissionService;
      case 'tireService':
        return l10n.tireService;
      case 'batteryService':
        return l10n.batteryService;
      case 'airConditioning':
        return l10n.airConditioning;
      case 'electricalSystem':
        return l10n.electricalSystem;
      case 'suspension':
        return l10n.suspension;
      case 'exhaustSystem':
        return l10n.exhaustSystem;
      case 'fuelSystem':
        return l10n.fuelSystem;
      case 'coolingSystem':
        return l10n.coolingSystem;
      case 'regularMaintenance':
        return l10n.regularMaintenance;
      case 'other':
        return l10n.other;
      default:
        return l10n.generalService;
    }
  }

  @override
  void initState() {
    super.initState();
    _loadMaintenanceData();
  }

  Future<void> _loadMaintenanceData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final maintenanceList =
          await ref.read(maintenanceProvider(widget.carId).future);
      final maintenance = maintenanceList.firstWhere(
        (m) => m.id == widget.maintenanceId,
        orElse: () => throw Exception('Maintenance record not found'),
      );

      setState(() {
        _originalMaintenance = maintenance;
        _descriptionController.text = maintenance.description;
        _mileageController.text = maintenance.mileage.toString();
        _costController.text = maintenance.cost.toString();
        _serviceProviderController.text = maintenance.serviceProvider;
        _notesController.text = maintenance.notes;
        _date = maintenance.date;
        _selectedMaintenanceType = maintenance.maintenanceType;
        _existingPhotoUrls = List<String>.from(maintenance.photoUrls);
        _dataLoaded = true;
        _isLoading = false;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading maintenance: $e'),
            backgroundColor: context.secondaryAccentColor,
          ),
        );
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _mileageController.dispose();
    _costController.dispose();
    _serviceProviderController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  // Handler for when a photo is selected
  void _handlePhotoSelected(File photoFile) {
    setState(() {
      _selectedPhotoFiles.add(photoFile);
    });
  }

  // Handler for removing an existing photo
  void _handleRemoveExistingPhoto(String photoUrl) {
    setState(() {
      _existingPhotoUrls.remove(photoUrl);
      _photosToDelete.add(photoUrl);
    });
  }

  // Handler for removing a newly selected photo file
  void _handleRemoveSelectedPhoto(int index) {
    if (index >= 0 && index < _selectedPhotoFiles.length) {
      setState(() {
        _selectedPhotoFiles.removeAt(index);
      });
    }
  }

  Future<void> _updateMaintenance() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) {
        final l10n = S.of(context);
        return AlertDialog(
          backgroundColor: context.containerBackgroundColor,
          title: Text(
            l10n.saveChanges,
            style: TextStyle(color: context.accentColor),
          ),
          content: Text(
            "${l10n.saveChanges}?",
            style: TextStyle(color: context.primaryTextColor),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                l10n.cancel,
                style: TextStyle(color: context.accentColor),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: context.secondaryAccentColor,
              ),
              child: Text(
                l10n.save,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );

    if (confirmed != true) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Check authentication status
      User? currentUser;
      try {
        currentUser = FirebaseAuth.instance.currentUser;

        if (currentUser == null) {
          final authState = ref.read(authStateChangesProvider).value;
          if (authState != null) {
            currentUser = authState;
          } else {
            throw Exception('User not authenticated. Please sign in again.');
          }
        }

        String? currentToken = await currentUser.getIdToken(false);
        if (currentToken?.isEmpty ?? true) {
          await currentUser.getIdToken(true);
        }

        if (FirebaseAuth.instance.currentUser == null) {
          throw Exception('Authentication error. Please sign in again.');
        }
      } catch (authError) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(authError.toString().contains('sign in again')
                  ? S.of(context).authenticationError
                  : '${S.of(context).authenticationError}: ${authError.toString().split('Exception:').last}'),
              backgroundColor: context.secondaryAccentColor,
              duration: const Duration(seconds: 5),
              action: SnackBarAction(
                label: S.of(context).signInAgain,
                onPressed: () => context.go('/login'),
              ),
            ),
          );
          setState(() => _isLoading = false);
        }
        return;
      }

      if (_originalMaintenance == null) {
        throw Exception('Original maintenance data not found');
      }

      // Get the updated maintenance model
      final updatedMaintenance = _originalMaintenance!.copyWith(
        description: _descriptionController.text,
        maintenanceType: _selectedMaintenanceType,
        mileage: int.parse(_mileageController.text),
        cost: double.parse(
            _costController.text.isEmpty ? '0' : _costController.text),
        serviceProvider: _serviceProviderController.text,
        notes: _notesController.text,
        date: _date,
        updatedAt: DateTime.now(),
      );

      // Get the repository
      final maintenanceRepo = ref.read(maintenanceRepositoryProvider);
      // Set the localization object on the repository
      maintenanceRepo.setL10n(S.of(context));

      // Delete removed photos
      for (final photoUrl in _photosToDelete) {
        await maintenanceRepo.deleteMaintenancePhoto(photoUrl);
      }

      // Upload new photos
      final List<String> newPhotoUrls = List<String>.from(_existingPhotoUrls);
      for (final photoFile in _selectedPhotoFiles) {
        try {
          dev.log('EditMaintenanceScreen: Uploading photo: ${photoFile.path}');

          // Check if file exists and is readable
          if (!await photoFile.exists()) {
            dev.log(
                'EditMaintenanceScreen: Photo file does not exist: ${photoFile.path}');
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                    content:
                        Text('Error: Photo file not found or inaccessible')),
              );
            }
            continue;
          }

          final photoUrl = await maintenanceRepo.uploadMaintenancePhoto(
              photoFile, widget.carId, widget.maintenanceId);

          if (photoUrl != null) {
            newPhotoUrls.add(photoUrl);
            dev.log(
                'EditMaintenanceScreen: Photo uploaded successfully: $photoUrl');
          } else {
            dev.log(
                'EditMaintenanceScreen: Failed to upload photo: ${photoFile.path}');
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Failed to upload photo')),
              );
            }
          }
        } catch (uploadError) {
          dev.log(
              'EditMaintenanceScreen: Error during photo upload: $uploadError');
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                  content: Text(
                      'Error uploading photo: ${uploadError.toString().split('Exception:').last}')),
            );
          }
        }
      }

      // Update the maintenance record with updated photo URLs
      final finalUpdatedMaintenance =
          updatedMaintenance.copyWith(photoUrls: newPhotoUrls);
      await maintenanceRepo.updateMaintenance(finalUpdatedMaintenance);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(S.of(context).maintenanceUpdated)),
        );
        context.pop(true);
      }
    } catch (e) {
      dev.log('EditMaintenanceScreen: Error updating maintenance: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                '${S.of(context).error}: ${e.toString().split('Exception:').last}'),
            backgroundColor: context.secondaryAccentColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = S.of(context);
    final carsAsync = ref.watch(carsProvider);

    return Scaffold(
      backgroundColor: context.containerBackgroundColor,
      appBar: AppBar(
        title: Text(
          l10n.editMaintenance,
          style: TextStyle(
            color: context.accentColor,
            fontWeight: FontWeight.bold,
            fontSize: 22,
          ),
        ),
        backgroundColor: context.containerBackgroundColor,
        elevation: 0,
        iconTheme: IconThemeData(color: context.accentColor),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              context.containerBackgroundColor,
              context.isDarkMode
                  ? Color(0xFF2A2727) // Slightly darker shade for dark mode
                  : Colors.grey.shade100, // Lighter shade for light mode
            ],
          ),
        ),
        child: Stack(
          children: [
            if (!_dataLoaded) ...[
              Center(
                child: CircularProgressIndicator(
                  valueColor:
                      AlwaysStoppedAnimation<Color>(context.accentColor),
                ),
              )
            ] else ...[
              SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildFormFields(context, l10n),
                    ],
                  ),
                ),
              ),
            ],
            if (_isLoading)
              Container(
                color: Colors.black.withOpacity(0.3),
                child: Center(
                  child: CircularProgressIndicator(
                    valueColor:
                        AlwaysStoppedAnimation<Color>(context.accentColor),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildFormFields(BuildContext context, S l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Maintenance Type
        Text(
          l10n.maintenanceType,
          style: TextStyle(
            color: context.accentColor,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: context.accentColor.withOpacity(0.5)),
            borderRadius: BorderRadius.circular(8),
            color: context.containerBackgroundColor.withOpacity(0.5),
          ),
          child: DropdownButtonFormField<String>(
            value: _selectedMaintenanceType,
            decoration: InputDecoration(
              filled: true,
              fillColor: context.containerBackgroundColor.withOpacity(0.5),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide.none,
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide.none,
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide.none,
              ),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            ),
            style: TextStyle(color: context.primaryTextColor),
            dropdownColor: context.containerBackgroundColor,
            items: _getMaintenanceTypes().map((type) {
              return DropdownMenuItem(
                value: type,
                child: Text(_getLocalizedMaintenanceType(type, l10n)),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedMaintenanceType = value;
                });
              }
            },
          ),
        ),
        const SizedBox(height: 16),

        // Description
        CustomTextField(
          controller: _descriptionController,
          labelText: l10n.maintenanceDescription,
          maxLines: 3,
          prefixIcon: const Icon(Icons.description),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return l10n.maintenanceDescription;
            }
            return null;
          },
        ),
        const SizedBox(height: 16),

        // Mileage
        CustomTextField(
          controller: _mileageController,
          labelText: l10n.mileage,
          keyboardType: TextInputType.number,
          prefixIcon: const Icon(Icons.speed),
          suffixIcon: Padding(
            padding: const EdgeInsets.only(right: 12),
            child:
                Text('KM', style: TextStyle(color: context.secondaryTextColor)),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return l10n.pleaseEnterMileage;
            }
            final mileage = int.tryParse(value);
            if (mileage == null) {
              return l10n.invalidMileage;
            }
            return null;
          },
        ),
        const SizedBox(height: 16),

        // Cost
        CustomTextField(
          controller: _costController,
          labelText: l10n.maintenanceCost,
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          prefixIcon: const Icon(Icons.attach_money),
        ),
        const SizedBox(height: 16),

        // Service Provider
        CustomTextField(
          controller: _serviceProviderController,
          labelText: l10n.maintenanceProvider,
          prefixIcon: const Icon(Icons.store),
        ),
        const SizedBox(height: 16),

        // Notes
        CustomTextField(
          controller: _notesController,
          labelText: '${l10n.notes} (${l10n.optional})',
          maxLines: 3,
          prefixIcon: const Icon(Icons.note),
        ),
        const SizedBox(height: 16),

        // Date
        Text(
          l10n.date,
          style: TextStyle(
            color: context.accentColor,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        _buildDatePicker(context),
        const SizedBox(height: 16),

        // Existing Photos Section
        if (_existingPhotoUrls.isNotEmpty) ...[
          Text(
            l10n.existingPhotos,
            style: TextStyle(
              color: context.accentColor,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          MaintenancePhotoPicker(
            photoUrls: _existingPhotoUrls,
            onPhotoSelected: (_) {}, // Not used for existing photos
            onPhotoRemoved: _handleRemoveExistingPhoto,
            isEditing: true,
            showTitle: false,
            showButtons: false,
          ),
          const SizedBox(height: 16),
        ],

        // New Photos Section
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              _selectedPhotoFiles.isEmpty
                  ? l10n.addNewPhotos
                  : '${l10n.photos} (${_selectedPhotoFiles.length})',
              style: TextStyle(
                color: context.accentColor,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            Row(
              children: [
                InkWell(
                  onTap: () async {
                    final maintenancePhotoService =
                        ref.read(maintenancePhotoServiceProvider);
                    final hasPermission =
                        await AppPermissionHandler.requestCameraPermission(
                            context);
                    if (!hasPermission) return;

                    try {
                      final photoFile =
                          await maintenancePhotoService.takePhoto();
                      if (photoFile != null && mounted) {
                        _handlePhotoSelected(photoFile);
                      }
                    } catch (e) {
                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text(l10n.failedToTakePhoto)),
                        );
                      }
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: context.accentColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(Icons.camera_alt, color: context.accentColor),
                  ),
                ),
                const SizedBox(width: 8),
                InkWell(
                  onTap: () async {
                    final maintenancePhotoService =
                        ref.read(maintenancePhotoServiceProvider);
                    final hasPermission =
                        await AppPermissionHandler.requestStoragePermission(
                            context);
                    if (!hasPermission) return;

                    try {
                      final photoFile =
                          await maintenancePhotoService.pickImage();
                      if (photoFile != null && mounted) {
                        _handlePhotoSelected(photoFile);
                      }
                    } catch (e) {
                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text(l10n.failedToSelectPhoto)),
                        );
                      }
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: context.accentColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child:
                        Icon(Icons.photo_library, color: context.accentColor),
                  ),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 8),
        MaintenancePhotoPicker(
          photoUrls: _selectedPhotoFiles.map((file) => file.path).toList(),
          onPhotoSelected: _handlePhotoSelected,
          onPhotoRemoved: (path) {
            final index =
                _selectedPhotoFiles.indexWhere((file) => file.path == path);
            if (index >= 0) {
              _handleRemoveSelectedPhoto(index);
            }
          },
          showTitle: false,
          showButtons: false,
        ),
        const SizedBox(height: 24),

        // Save Button with gradient styling
        Container(
          height: 56,
          margin: const EdgeInsets.only(top: 8, bottom: 20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                context.secondaryAccentColor,
                context.secondaryAccentColor.withOpacity(0.8),
              ],
            ),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: context.secondaryAccentColor.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: _isLoading ? null : _updateMaintenance,
              borderRadius: BorderRadius.circular(12),
              child: Center(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.save,
                      color: Colors.white,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _isLoading ? l10n.loading : l10n.saveChanges,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                        letterSpacing: 0.5,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDatePicker(BuildContext context) {
    return InkWell(
      onTap: () async {
        final picked = await showDatePicker(
          context: context,
          initialDate: _date,
          firstDate: DateTime(2000),
          lastDate: DateTime.now(),
          builder: (context, child) => Theme(
            data: Theme.of(context).copyWith(
              colorScheme: ColorScheme.dark(
                primary: context.accentColor,
                onPrimary: context.primaryTextColor,
                surface: context.containerBackgroundColor,
                onSurface: context.primaryTextColor,
              ),
              dialogTheme: DialogTheme(
                backgroundColor: context.containerBackgroundColor,
              ),
            ),
            child: child!,
          ),
        );
        if (picked != null) {
          setState(() => _date = picked);
        }
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        decoration: BoxDecoration(
          color: context.containerBackgroundColor.withOpacity(0.5),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: context.accentColor.withOpacity(0.5)),
        ),
        child: Row(
          children: [
            Icon(Icons.calendar_today, color: context.accentColor),
            const SizedBox(width: 16),
            Text(
              DateFormat.yMMMd().format(_date),
              style: TextStyle(color: context.primaryTextColor, fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }
}
