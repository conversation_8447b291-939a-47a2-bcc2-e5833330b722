import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../core/theme/theme_extensions.dart';

class CustomTextField extends StatelessWidget {
  final TextEditingController controller;
  final String labelText;
  final String? placeholder;
  final TextInputType keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final String? Function(String?)? validator;
  final bool obscureText;
  final Widget? suffixIcon;
  final Widget? prefixIcon;
  final TextCapitalization textCapitalization;
  final int? maxLines;
  final int? minLines;
  final bool readOnly;
  final GestureTapCallback? onTap;
  final void Function(String)? onChanged;
  final FocusNode? focusNode;
  final bool autofocus;
  final String? errorText;
  final TextStyle? textStyle;
  final TextAlign textAlign;
  final bool alignLabelWithHint;

  const CustomTextField({
    super.key,
    required this.controller,
    required this.labelText,
    this.placeholder,
    this.keyboardType = TextInputType.text,
    this.inputFormatters,
    this.validator,
    this.obscureText = false,
    this.suffixIcon,
    this.prefixIcon,
    this.textCapitalization = TextCapitalization.none,
    this.maxLines = 1,
    this.minLines,
    this.readOnly = false,
    this.onTap,
    this.onChanged,
    this.focusNode,
    this.autofocus = false,
    this.errorText,
    this.textStyle,
    this.textAlign = TextAlign.start,
    this.alignLabelWithHint = false,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      inputFormatters: inputFormatters,
      validator: validator,
      obscureText: obscureText,
      maxLines: maxLines,
      minLines: minLines,
      readOnly: readOnly,
      onTap: onTap,
      onChanged: onChanged,
      focusNode: focusNode,
      autofocus: autofocus,
      textCapitalization: textCapitalization,
      textAlign: textAlign,
      style: textStyle ?? TextStyle(color: context.primaryTextColor),
      decoration: InputDecoration(
        labelText: labelText,
        hintText: placeholder,
        alignLabelWithHint: alignLabelWithHint,
        hintStyle: TextStyle(color: context.secondaryTextColor.withOpacity(0.7)),
        labelStyle: TextStyle(color: context.accentColor),
        errorText: errorText,
        errorStyle: const TextStyle(color: Colors.red),
        suffixIcon: suffixIcon,
        prefixIcon: prefixIcon,
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: context.accentColor.withOpacity(0.5)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: context.accentColor),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.red),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        filled: true,
        fillColor: context.containerBackgroundColor.withOpacity(0.5),
      ),
    );
  }
} 